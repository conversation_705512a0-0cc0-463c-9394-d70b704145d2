import type { ChangeEvent } from "react"
import { CheckboxGroup, CheckboxGroupItem } from "../ui/checkbox"

interface Props {
  handleSeatSelect: ({
    e,
    seat,
  }: {
    e: ChangeEvent<HTMLInputElement>
    seat: string
  }) => void
  seatNumbers: string[]
}

const VanapaHallFront = ({ handleSeatSelect, seatNumbers }: Props) => {
  return (
    <>
      {/* F152-175 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 152}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 164}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      {/* F128 - 151 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 128}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 140}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      {/* F106 - 127 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 1 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 105}`}
              >
                F{index + 105}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 10 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 117}`}
              >
                F{index + 117}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* F84 - 105 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 1 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 83}`}
              >
                F{index + 83}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 10 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 95}`}
              >
                F{index + 95}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* F62 - 83 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 1 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 61}`}
              >
                F{index + 61}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 10 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 73}`}
              >
                F{index + 73}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* F41 - 82 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 2 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 39}`}
              >
                F{index + 39}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 10 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 51}`}
              >
                F{index + 51}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* F21 - 40 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 2 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 19}`}
              >
                F{index + 19}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 9 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 31}`}
              >
                F{index + 31}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* F1 - 20 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 2 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index - 1}`}
              >
                F{index - 1}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 9 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 11}`}
              >
                F{index + 11}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
    </>
  )
}

export default VanapaHallFront
