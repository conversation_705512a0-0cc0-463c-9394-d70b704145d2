import { Button } from "../ui/button"
import {
  TransformWrapper,
  TransformComponent,
  useControls,
} from "react-zoom-pan-pinch"
import VanapaHallGallery from "./vanapa-hall-gallery"
import VanapaHallBack from "./vanapa-hall-back"
import Vanapa<PERSON><PERSON><PERSON><PERSON> from "./vanapa-hall-middle"
import Vanapa<PERSON><PERSON>Front from "./vanapa-hall-front"
import VanapaHallVip from "./vanapa-hall-vip"
import type { ChangeEvent } from "react"

const Controls = () => {
  const { zoomIn, zoomOut, resetTransform } = useControls()

  return (
    <div className="flex justify-end gap-x-4">
      <Button variant="outline" onClick={() => zoomIn()}>
        +
      </Button>
      <Button variant="outline" onClick={() => zoomOut()}>
        -
      </Button>
      <Button variant="outline" onClick={() => resetTransform()}>
        x
      </Button>
    </div>
  )
}

interface Props {
  handleSeatSelect: ({
    e,
    seat,
  }: {
    e: ChangeEvent<HTMLInputElement>
    seat: string
  }) => void
  seatNumbers: string[]
}

const VanapaHall = ({ handleSeatSelect, seatNumbers }: Props) => {
  return (
    <TransformWrapper
      initialScale={0.5}
      minScale={0.5}
      maxScale={3}
      centerOnInit={true}
      wheel={{ step: 0.1 }}
      panning={{
        disabled: false,
        velocityDisabled: false,
        excluded: ["input", "label", "button"],
      }}
      doubleClick={{ disabled: false }}
      limitToBounds={true}
      smooth={true}
      alignmentAnimation={{ sizeX: 0, sizeY: 0, velocityAlignmentTime: 0 }}
    >
      {() => (
        <>
          <Controls />
          <div className="relative h-[500px] w-full overflow-hidden rounded-lg border border-gray-200">
            <TransformComponent
              wrapperClass="!w-full !h-full"
              contentClass="!flex !items-center !justify-center"
            >
              <div className="grid min-h-[500px] min-w-[1280px] grid-cols-11 gap-1 p-4 text-xs font-bold">
                <VanapaHallGallery
                  seatNumbers={seatNumbers}
                  handleSeatSelect={handleSeatSelect}
                />
                <div className="col-span-11 my-4" />
                <VanapaHallBack />
                <VanapaHallMiddle />
                <div className="col-span-11 my-4" />
                <VanapaHallFront />
                <div className="col-span-11 my-4" />
                {/* <VanapaHallGallery /> */}
                <VanapaHallVip />
                <div className="col-span-11 my-4" />
              </div>
            </TransformComponent>
          </div>
        </>
      )}
    </TransformWrapper>
  )
}

export default VanapaHall
