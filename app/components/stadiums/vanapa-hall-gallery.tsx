import type { ChangeEvent } from "react"
import { CheckboxGroup, CheckboxGroupItem } from "../ui/checkbox"

interface Props {
  handleSeatSelect: ({
    e,
    seat,
  }: {
    e: ChangeEvent<HTMLInputElement>
    seat: string
  }) => void
  seatNumbers: string[]
}

const VanapaHallGallery = ({ handleSeatSelect, seatNumbers }: Props) => {
  return (
    <>
      {/* 133 - 148 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 128}`
            if (index < 5) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 8 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={`G${index + 140}`}
                id={`G${index + 140}`}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`G${index + 140}`}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: `G${index + 140}` })
                }}
              >
                G{index + 140}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* 115 - 132 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 3 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                id={`G${index + 112}`}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`G${index + 112}`}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: `G${index + 112}` })
                }}
              >
                G{index + 112}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 8 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                id={`G${index + 124}`}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`G${index + 124}`}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: `G${index + 124}` })
                }}
              >
                G{index + 124}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* 95 - 114 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 2 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                id={`G${index + 93}`}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`G${index + 93}`}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: `G${index + 93}` })
                }}
              >
                G{index + 93}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 9 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                id={`G${index + 105}`}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`G${index + 105}`}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: `G${index + 105}` })
                }}
              >
                G{index + 105}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* 73 - 94 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index === 0 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                id={`G${index + 72}`}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`G${index + 72}`}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: `G${index + 72}` })
                }}
              >
                G{index + 72}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 10 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                id={`G${index + 84}`}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`G${index + 84}`}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: `G${index + 84}` })
                }}
              >
                G{index + 84}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              id={`G${index + 49}`}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`G${index + 49}`}
              onChange={(e) => {
                handleSeatSelect({ e, seat: `G${index + 49}` })
              }}
            >
              G{index + 49}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              id={`G${index + 61}`}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`G${index + 61}`}
              onChange={(e) => {
                handleSeatSelect({ e, seat: `G${index + 61}` })
              }}
            >
              G{index + 61}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>

      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              id={`G${index + 25}`}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`G${index + 25}`}
              onChange={(e) => {
                handleSeatSelect({ e, seat: `G${index + 25}` })
              }}
            >
              G{index + 25}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              id={`G${index + 37}`}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`G${index + 37}`}
              onChange={(e) => {
                handleSeatSelect({ e, seat: `G${index + 37}` })
              }}
            >
              G{index + 37}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>

      <div className="col-span-5 ">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              id={`G${index + 1}`}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`G${index + 1}`}
              onChange={(e) => {
                handleSeatSelect({ e, seat: `G${index + 1}` })
              }}
            >
              G{index + 1}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              id={`G${index + 13}`}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`G${index + 13}`}
              onChange={(e) => {
                handleSeatSelect({ e, seat: `G${index + 13}` })
              }}
            >
              G{index + 13}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
    </>
  )
}

export default VanapaHallGallery
